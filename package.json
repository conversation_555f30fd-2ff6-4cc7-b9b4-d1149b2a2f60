{"name": "book-trading-platform", "version": "1.0.0", "description": "大学生收书卖书交易平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "test": "jest", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed"}, "keywords": ["books", "trading", "university", "marketplace", "auction"], "author": "BookTrading Team", "license": "MIT", "devDependencies": {"@types/jest": "^30.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "concurrently": "^7.6.0", "jest": "^29.0.0"}, "dependencies": {"@tanstack/react-query": "^5.83.0", "antd": "^5.26.6", "moment": "^2.30.1", "react-router-dom": "^7.7.0"}}