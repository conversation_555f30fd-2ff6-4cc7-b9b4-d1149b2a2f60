<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收书卖书 - 大学生二手书籍交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .feature-card h3 {
            color: #2d3748;
            font-size: 1.4em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .feature-card .icon {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .ui-showcase {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .ui-showcase h2 {
            color: #2d3748;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
        }

        .ui-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .ui-component {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            transition: border-color 0.3s ease;
        }

        .ui-component:hover {
            border-color: #667eea;
        }

        .ui-component h4 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .ui-component .file-path {
            font-family: monospace;
            background: #e2e8f0;
            padding: 5px 8px;
            border-radius: 5px;
            font-size: 0.85em;
            color: #4a5568;
            margin-bottom: 10px;
            display: block;
        }

        .deployment-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .deployment-section h2 {
            color: #2d3748;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
        }

        .deployment-steps {
            list-style: none;
            counter-reset: step-counter;
        }

        .deployment-steps li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            position: relative;
            padding-left: 50px;
        }

        .deployment-steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .tech-stack {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .tech-stack h2 {
            color: #2d3748;
            font-size: 2em;
            margin-bottom: 20px;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .tech-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .tech-tag:hover {
            transform: scale(1.05);
        }

        footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 40px;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .ui-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>📚 收书卖书</h1>
            <p class="subtitle">大学生二手书籍交易平台 - 让知识流转更简单</p>
        </header>

        <div class="features">
            <div class="feature-card">
                <h3><span class="icon">🛒</span>图书交易</h3>
                <p>支持图书买卖、拍卖、批量采购等多种交易方式，满足不同用户需求。</p>
            </div>
            <div class="feature-card">
                <h3><span class="icon">🎓</span>毕业季特区</h3>
                <p>专为毕业生设计的图书处理功能，快速清理毕业季囤积的教材。</p>
            </div>
            <div class="feature-card">
                <h3><span class="icon">📋</span>课程大纲</h3>
                <p>根据专业课程大纲推荐相关教材，精准匹配学习需求。</p>
            </div>
            <div class="feature-card">
                <h3><span class="icon">📍</span>交易点功能</h3>
                <p>校园内设置多个交易点，方便线下交易和图书交换。</p>
            </div>
            <div class="feature-card">
                <h3><span class="icon">⚡</span>急需图书</h3>
                <p>发布急需图书信息，快速找到所需教材和参考书。</p>
            </div>
            <div class="feature-card">
                <h3><span class="icon">🔍</span>智能搜索</h3>
                <p>支持多维度搜索，快速定位所需图书信息。</p>
            </div>
        </div>

        <div class="ui-showcase">
            <h2>🎨 界面设计文件</h2>
            <div class="ui-grid">
                <div class="ui-component">
                    <h4>主页面</h4>
                    <span class="file-path">frontend/src/pages/home/<USER>/span>
                    <p>平台主页，展示热门图书和功能导航</p>
                </div>
                <div class="ui-component">
                    <h4>图书列表</h4>
                    <span class="file-path">frontend/src/pages/books/BookListPage.tsx</span>
                    <p>图书浏览页面，支持筛选和排序</p>
                </div>
                <div class="ui-component">
                    <h4>图书详情</h4>
                    <span class="file-path">frontend/src/pages/books/BookDetailPage.tsx</span>
                    <p>图书详细信息展示页面</p>
                </div>
                <div class="ui-component">
                    <h4>用户认证</h4>
                    <span class="file-path">frontend/src/pages/auth/LoginPage.tsx</span>
                    <p>用户登录注册界面</p>
                </div>
                <div class="ui-component">
                    <h4>拍卖功能</h4>
                    <span class="file-path">frontend/src/pages/auctions/AuctionListPage.tsx</span>
                    <p>图书拍卖列表和详情页面</p>
                </div>
                <div class="ui-component">
                    <h4>订单管理</h4>
                    <span class="file-path">frontend/src/pages/orders/OrderListPage.tsx</span>
                    <p>用户订单查看和管理界面</p>
                </div>
                <div class="ui-component">
                    <h4>个人中心</h4>
                    <span class="file-path">frontend/src/pages/profile/ProfilePage.tsx</span>
                    <p>用户个人信息和设置页面</p>
                </div>
                <div class="ui-component">
                    <h4>毕业季专区</h4>
                    <span class="file-path">frontend/src/pages/graduation/GraduationSalePage.tsx</span>
                    <p>毕业生图书处理专用界面</p>
                </div>
                <div class="ui-component">
                    <h4>课程大纲</h4>
                    <span class="file-path">frontend/src/pages/syllabus/SyllabusPage.tsx</span>
                    <p>专业课程大纲和教材推荐</p>
                </div>
                <div class="ui-component">
                    <h4>交易点</h4>
                    <span class="file-path">frontend/src/pages/trading/TradingPointsPage.tsx</span>
                    <p>校园交易点位置和使用管理</p>
                </div>
                <div class="ui-component">
                    <h4>急需图书</h4>
                    <span class="file-path">frontend/src/pages/urgent/UrgentBooksPage.tsx</span>
                    <p>发布和查看急需图书信息</p>
                </div>
                <div class="ui-component">
                    <h4>增强搜索</h4>
                    <span class="file-path">frontend/src/pages/search/EnhancedSearchPage.tsx</span>
                    <p>高级搜索功能界面</p>
                </div>
            </div>
        </div>

        <div class="deployment-section">
            <h2>🚀 部署说明</h2>
            <ol class="deployment-steps">
                <li>项目已复制到 <strong>收书卖书_部署版</strong> 文件夹</li>
                <li>进入前端目录：<code>cd frontend</code></li>
                <li>安装依赖：<code>npm install</code></li>
                <li>启动开发服务器：<code>npm run dev</code></li>
                <li>进入后端目录：<code>cd backend</code></li>
                <li>安装后端依赖：<code>npm install</code></li>
                <li>配置数据库连接</li>
                <li>启动后端服务：<code>npm start</code></li>
            </ol>
            <p style="margin-top: 20px; color: #666; text-align: center;">
                注意：项目目前存在一些TypeScript类型错误，需要进一步修复才能正常构建和运行。
            </p>
        </div>

        <div class="tech-stack">
            <h2>🛠️ 技术栈</h2>
            <div class="tech-tags">
                <span class="tech-tag">React</span>
                <span class="tech-tag">TypeScript</span>
                <span class="tech-tag">Vite</span>
                <span class="tech-tag">Ant Design</span>
                <span class="tech-tag">Node.js</span>
                <span class="tech-tag">Express</span>
                <span class="tech-tag">Prisma</span>
                <span class="tech-tag">PostgreSQL</span>
                <span class="tech-tag">Redis</span>
                <span class="tech-tag">WebSocket</span>
                <span class="tech-tag">Docker</span>
                <span class="tech-tag">Jest</span>
            </div>
        </div>

        <footer>
            <p>© 2024 收书卖书平台 - 专为大学生打造的二手书籍交易平台</p>
        </footer>
    </div>
</body>
</html>