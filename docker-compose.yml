version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: bookmarket-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-bookmarket123}
      MYSQL_DATABASE: bookmarket
      MYSQL_USER: bookmarket
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-bookmarket123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - bookmarket-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bookmarket-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-bookmarket123}
    networks:
      - bookmarket-network

  # ElasticSearch搜索引擎
  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: bookmarket-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    networks:
      - bookmarket-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bookmarket-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://bookmarket:${MYSQL_PASSWORD:-bookmarket123}@mysql:3306/bookmarket
      - REDIS_URL=redis://:${REDIS_PASSWORD:-bookmarket123}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
    volumes:
      - upload_data:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      - mysql
      - redis
      - elasticsearch
    networks:
      - bookmarket-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bookmarket-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - bookmarket-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: bookmarket-nginx
    restart: unless-stopped
    ports:
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - bookmarket-network

volumes:
  mysql_data:
  redis_data:
  es_data:
  upload_data:

networks:
  bookmarket-network:
    driver: bridge