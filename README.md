# 大学生收书卖书交易平台

## 项目简介
一个专为大学生设计的二手书籍交易平台，提供买卖、拍卖、社交、智能推荐等全方位服务。

## 核心功能
- 📚 智能图书识别和自动填充
- 🔨 实时拍卖系统
- 🤖 AI智能推荐
- 💬 社区交流和评价
- 📱 移动端友好的响应式设计
- 💰 多种支付方式支持

## 技术栈
- **前端**: React 18 + TypeScript + Vite + Ant Design
- **后端**: Node.js + Express + TypeScript + Prisma
- **数据库**: MySQL 8.0 + Redis + ElasticSearch
- **部署**: Docker + Nginx

## 开发规划
- **第1-16周**: 基础功能开发
- **第17-40周**: 高级功能和AI智能化升级

## 快速开始
```bash
# 安装依赖
npm install

# 启动开发环境
npm run dev
```

## 项目结构
```
收书卖书/
├── frontend/          # 前端React应用
├── backend/           # 后端API服务
├── database/          # 数据库脚本
├── docs/             # 项目文档
└── docker/           # Docker配置
```