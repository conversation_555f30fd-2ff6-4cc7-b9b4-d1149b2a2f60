# 快速启动指南

## 项目概述

这是一个专为大学生设计的二手书籍交易平台，包含以下核心功能：

- 📚 **智能书籍识别**：AI拍照识别书籍信息
- 🔨 **实时拍卖系统**：WebSocket实时竞拍
- 🤖 **智能推荐**：基于用户行为的个性化推荐
- 💬 **社区交流**：留言评价和学习小组
- 📱 **响应式设计**：支持PC和移动端

## 技术栈

- **前端**: React 18 + TypeScript + Vite + Ant Design
- **后端**: Node.js + Express + Prisma + MySQL
- **缓存**: Redis
- **搜索**: ElasticSearch
- **实时通信**: Socket.io
- **部署**: Docker + Nginx

## 快速开始

### 环境要求

- Node.js 18+
- MySQL 8.0+
- Redis 7+
- Docker & Docker Compose（可选）

### 1. 克隆项目

```bash
git clone <repository-url>
cd 收书卖书
```

### 2. 环境配置

复制环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接等信息。

### 3. 使用Docker启动（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 4. 手动启动开发环境

#### 4.1 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

#### 4.2 启动数据库

```bash
# 启动MySQL和Redis（使用Docker）
docker-compose up -d mysql redis elasticsearch

# 或者使用本地安装的数据库服务
```

#### 4.3 数据库初始化

```bash
cd backend

# 生成Prisma客户端
npx prisma generate

# 创建数据库表
npx prisma db push

# 导入初始数据
mysql -u root -p bookmarket < ../database/init.sql
```

#### 4.4 启动应用

```bash
# 在项目根目录启动（会同时启动前后端）
npm run dev

# 或者分别启动
# 后端服务 (端口3000)
cd backend && npm run dev

# 前端服务 (端口5173)
cd frontend && npm run dev
```

## 访问地址

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/api-docs

## 默认账户

开发环境包含以下测试账户：

| 用户名 | 邮箱 | 密码 | 角色 |
|--------|------|------|------|
| 数学小王子 | <EMAIL> | 123456 | 学生 |
| 编程爱好者 | <EMAIL> | 123456 | 学生 |
| 英语达人 | <EMAIL> | 123456 | 学生 |

## 项目结构

```
收书卖书/
├── frontend/                 # 前端React应用
│   ├── src/
│   │   ├── components/       # 公共组件
│   │   ├── pages/           # 页面组件
│   │   ├── hooks/           # 自定义Hooks
│   │   ├── services/        # API服务
│   │   ├── store/           # 状态管理
│   │   └── utils/           # 工具函数
│   └── public/              # 静态资源
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── services/        # 业务逻辑
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由定义
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   └── prisma/              # 数据库模式
├── database/                # 数据库脚本
├── docker/                  # Docker配置
└── docs/                    # 项目文档
```

## 开发指南

### 代码规范

- 使用TypeScript进行类型检查
- 使用ESLint和Prettier格式化代码
- 遵循React Hooks最佳实践
- 使用语义化的Git提交信息

### API开发

1. 在 `backend/src/routes/` 定义路由
2. 在 `backend/src/controllers/` 实现控制器逻辑
3. 在 `backend/src/services/` 编写业务逻辑
4. 使用Prisma进行数据库操作

### 前端开发

1. 在 `frontend/src/pages/` 创建页面组件
2. 在 `frontend/src/components/` 创建公共组件
3. 使用React Query管理服务端状态
4. 使用Zustand管理客户端状态

### 数据库操作

```bash
# 更新数据库结构
npx prisma db push

# 生成迁移文件
npx prisma migrate dev --name migration_name

# 查看数据库
npx prisma studio
```

## 部署指南

### Docker部署

```bash
# 构建并启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend frontend
```

### 手动部署

1. 构建前端应用：`cd frontend && npm run build`
2. 构建后端应用：`cd backend && npm run build`
3. 配置Nginx反向代理
4. 启动PM2进程管理：`pm2 start ecosystem.config.js`

## 常见问题

### Q: 数据库连接失败
A: 检查 `.env` 文件中的数据库配置，确保MySQL服务正在运行。

### Q: 前端无法访问后端API
A: 检查后端服务是否启动在3000端口，前端代理配置是否正确。

### Q: Docker容器启动失败
A: 检查Docker和Docker Compose版本，确保端口没有被占用。

### Q: 权限错误
A: 在Linux/Mac系统中，可能需要使用 `sudo` 权限或修改文件权限。

## 获取帮助

- 查看 [API文档](./docs/api.md)
- 查看 [开发指南](./docs/development.md)
- 提交 [Issue](https://github.com/your-repo/issues)
- 发送邮件至：<EMAIL>

## 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件。